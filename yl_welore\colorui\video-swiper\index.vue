<template>
    <view class="container">
        <swiper class="video-swiper" :circular="circular" easing-function="default" vertical :current="current" duration="200" @change="animationfinish">
            <!-- curQueue 循环会导致video重新插入，objectFit 不可变更 -->
            <swiper-item v-for="(item, index) in curQueue" :key="index">
                <video
                    :id="'video_' + index"
                    class="video_item"
                    :loop="loop"
                    :enable-play-gesture="false"
                    enable-progress-gesture
                    :show-center-play-btn="false"
                    :controls="false"
                    :custom-cache="false"
                    :src="item.url"
                    :data-id="item.id"
                    :object-fit="item.objectFit || 'contain'"
                    :data-index="index"
                    :data-url="item.url"
                    preload="metadata"
                    @play="onPlay"
                    @pause="onPause"
                    @ended="onEnded"
                    @error="onError"
                    @timeupdate="onTimeUpdate"
                    @waiting="onWaiting"
                    @progress="onProgress"
                    @loadedmetadata="onLoadedMetaData"
                    @loadstart="onLoadStart"
                    @canplay="onCanPlay"
                ></video>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
const app = getApp();
export default {
    data() {
        return {
            current: 0,
            nextQueue: [],
            prevQueue: [],
            curQueue: [],
            circular: false,
            _last: 0,
            _rawLast: 0,
            _videoContexts: [],
            newVals: []
        };
    },
    props: {
        duration: {
            type: Number,
            default: 500
        },
        easingFunction: {
            type: String,
            default: 'easeInOutCubic'
        },
        loop: {
            type: Boolean,
            default: true
        },
        videoListNewDom: {
            type: Array,
            default: () => []
        },
        videoList: {
            type: Array,
            default: () => []
        }
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
        // 初始化时创建 videoContext
        this.$nextTick(() => {
            if (this.curQueue.length > 0) {
                this.createvideoContexts();
            }
        });
    },
    methods: {
        attached() {},

        createvideoContexts(callback) {
            // 始终只为3个固定的视频元素创建 videoContext
            this._videoContexts = [];
            this.$nextTick(() => {
                for (let i = 0; i < 3; i++) {
                    try {
                        let navval = uni.createVideoContext('video_' + i, this);
                        if (navval) {
                            this._videoContexts.push(navval);
                        }
                    } catch (error) {
                        console.warn('创建视频上下文失败:', 'video_' + i, error);
                    }
                }
                console.log('视频上下文创建完成，数量:', this._videoContexts.length);
                // 创建完成后执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        },

        videoListChangedFun(newVal) {
            var that = this;
            var data = this;

            // 验证视频数据
            var validVideos = newVal.filter(item => {
                if (!item.url) {
                    console.warn('视频缺少URL:', item);
                    return false;
                }
                if (!item.id) {
                    console.warn('视频缺少ID:', item);
                    return false;
                }
                return true;
            });

            console.log('有效视频数量:', validVideos.length, '总数量:', newVal.length);

            if (validVideos.length > 0) {
                this.createvideoContexts();
            }
            validVideos.forEach(function (item) {
                data.nextQueue.push(item);
            });
            if (data.curQueue.length === 0) {
                this.curQueue = data.nextQueue.splice(0, Math.min(3, validVideos.length));
            }
        },

        animationfinish(e) {
            var u = app.globalData.getCache('userinfo');
            if (!u) {
                uni.showToast({
                    title: '更多内容请前往小程序查看',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (e.detail.current > 7) {
                this.current = 0;
                this.curQueue = [];
                this._videoContexts = [];
                this.nextQueue = [];
                this.prevQueue = [];
                this.circular = false;
                this._last = 0;
                this._rawLast = 0;
                this.newVals = [];
                // 重置后重新创建 videoContext
                this.$nextTick(() => {
                    this.createvideoContexts();
                });
            } else {
                // 保持原始的 current 值，不要映射
                this.current = e.detail.current;
            }
            uni.setStorageSync('slidecurrent', this.current);
            this.trigger(e, 'slide');
            var _data = this;
            var _last = _data._last;
            var curQueue = _data.curQueue;
            var prevQueue = _data.prevQueue;
            var nextQueue = _data.nextQueue;
            var current = this.current; // 原始的 current 值
            var rawLast = _data._rawLast || 0; // 上次的原始 current 值

            console.log('当前索引:', current, '上次索引:', rawLast);

            var diff = current - rawLast;
            if (diff === 0) {
                return;
            }

            // _last 应该是当前视频在 curQueue 中的位置 (0-2)
            var queueIndex = current % 3;
            this._last = queueIndex;
            this._rawLast = current;

            console.log('队列索引:', queueIndex, '_last:', this._last);
            console.log(current);
            console.log(curQueue);
            // this.triggerEvent('change', {
            //     activeId: curQueue[current].id
            // });
            // 简化队列管理逻辑 - 使用原始 current 值计算方向
            var direction = diff > 0 ? 'up' : 'down';
            console.log('滑动方向:', direction, 'diff:', diff);

            if (direction === 'up' && nextQueue.length > 0) {
                // 向上滑动，需要加载下一个视频
                var nextVideo = nextQueue.shift();
                var replaceIndex = (queueIndex + 2) % 3; // 替换最旧的视频位置
                var oldVideo = curQueue[replaceIndex];
                if (oldVideo) {
                    prevQueue.push(oldVideo);
                }
                curQueue[replaceIndex] = nextVideo;
                console.log('向上滑动，添加新视频到位置:', replaceIndex);
            } else if (direction === 'down' && prevQueue.length > 0) {
                // 向下滑动，需要加载上一个视频
                var prevVideo = prevQueue.pop();
                var replaceIndex = (queueIndex + 1) % 3; // 替换最新的视频位置
                var oldVideo = curQueue[replaceIndex];
                if (oldVideo) {
                    nextQueue.unshift(oldVideo);
                }
                curQueue[replaceIndex] = prevVideo;
                console.log('向下滑动，添加旧视频到位置:', replaceIndex);
            }
            var circular = true;
            if (nextQueue.length === 0 && queueIndex !== 0) {
                circular = false;
            }
            if (prevQueue.length === 0 && queueIndex !== 2) {
                circular = false;
            }
            this.curQueue = curQueue;
            this.circular = circular;
            // 队列内容变化后，重新创建 videoContext 并播放当前视频
            console.log('队列内容更新:', curQueue.map(item => ({ id: item?.id, url: item?.url?.substring(0, 50) + '...' })));
            this.createvideoContexts(() => {
                // videoContext 创建完成后播放当前视频
                console.log('准备播放视频，队列索引:', queueIndex, '当前队列长度:', this.curQueue.length);
                this.playCurrent(queueIndex);
            });
        },

        playCurrent(current) {
            console.log('playCurrent 被调用，current:', current, '_videoContexts 长度:', this._videoContexts ? this._videoContexts.length : 0);
            if (!this._videoContexts || this._videoContexts.length === 0) {
                console.warn('视频上下文未准备好，无法播放');
                return;
            }
            if (current < 0 || current >= this._videoContexts.length) {
                console.warn('current 索引超出范围:', current, '数组长度:', this._videoContexts.length);
                return;
            }
            this._videoContexts.forEach(function (ctx, index) {
                if (ctx) {
                    try {
                        if (index != current) {
                            console.log('停止视频:', index);
                            ctx.stop();
                        } else {
                            console.log('播放视频:', index);
                            ctx.play();
                        }
                    } catch (error) {
                        console.warn('视频播放控制失败:', index, error);
                    }
                } else {
                    console.warn('视频上下文为空:', index);
                }
            });
        },

        onPlay(e) {
            this.trigger(e, 'play');
        },

        onPause(e) {
            this.trigger(e, 'pause');
        },

        onEnded(e) {
            this.trigger(e, 'ended');
        },

        onError(e) {
            console.error('视频加载错误:', e);
            console.error('错误详情:', e.detail);
            console.error('视频索引:', e.target.dataset.index);
            console.error('视频ID:', e.target.dataset.id);

            // 获取当前视频信息
            var videoIndex = parseInt(e.target.dataset.index);
            if (this.curQueue && this.curQueue[videoIndex]) {
                console.error('视频URL:', this.curQueue[videoIndex].url);
                console.error('视频信息:', this.curQueue[videoIndex]);
            }

            // 显示用户友好的错误提示
            uni.showToast({
                title: '视频加载失败，请检查网络连接',
                icon: 'none',
                duration: 3000
            });

            this.trigger(e, 'error');
        },

        onTimeUpdate(e) {
            this.trigger(e, 'timeupdate');
        },

        onWaiting(e) {
            this.trigger(e, 'wait');
        },

        onProgress(e) {
            this.trigger(e, 'progress');
        },

        onLoadedMetaData(e) {
            console.log('视频元数据加载完成:', e.target.dataset.index, e.target.dataset.id);
            this.trigger(e, 'loadedmetadata');
        },

        onLoadStart(e) {
            console.log('开始加载视频:', e.target.dataset.index, e.target.dataset.url);
        },

        onCanPlay(e) {
            console.log('视频可以播放:', e.target.dataset.index, e.target.dataset.id);
        },

        trigger(e, type) {
            var ext = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
            var detail = e.detail;
            var activeId = e.target.dataset.id;
            this.$emit(type, {
                detail: Object.assign(
                    Object.assign(Object.assign({}, detail), {
                        activeId: activeId
                    }),
                    ext
                )
            });
        }
    },
    created: function () {},
    watch: {
        videoListNewDom: {
            handler() {
                var newValis = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
                if (this.curQueue.length > 0 && newValis.length > 0) {
                    this.createvideoContexts(() => {
                        // 新视频加载后，播放当前视频
                        this.playCurrent(this._last);
                    });
                }
            },

            immediate: true,
            deep: true
        },

        videoList: {
            handler() {
                var newVal = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
                if (this.curQueue.length > 0) {
                    this.curQueue = newVal;
                    // 队列内容变化后重新创建 videoContext
                    this.createvideoContexts(() => {
                        this.playCurrent(this._last);
                    });
                } else {
                    this.videoListChangedFun(newVal);
                }
            },

            immediate: true,
            deep: true
        }
    }
};

/***/
</script>
<style>
.container {
    width: 100%;
    height: 100vh;
}
.video-swiper {
    width: 100%;
    height: 100%;
}
.video_item {
    height: 100%;
    width: 100%;
}
</style>
