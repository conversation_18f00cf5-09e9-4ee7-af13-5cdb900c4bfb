<template>
    <view class="container">
        <!-- 内容列表 -->
        <view class="feed-list">
            <!-- 内容卡片 -->
            <view class="feed-card" v-for="(item, index) in new_list" :key="item.id">
                <!-- 顶部装饰条 - 不同内容类型显示不同颜色 -->
                <view class="feed-type-indicator" :class="'type-' + item.study_type"></view>

                <!-- 用户信息 -->
                <view class="user-info">
                    <image class="avatar" :src="item.user_head_sculpture" mode="widthFix"></image>
                    <view class="user-details">
                        <view class="username-row">
                            <text class="username">{{ item.user_nick_name }}</text>
                            <text class="user-badge" v-if="item.user_vip">VIP</text>
                        </view>
                        <view class="post-time">
                            {{ item.adapter_time }}
                            <text class="visibility" v-if="item.realm_name">· 发布于{{ item.realm_name }}</text>
                        </view>
                    </view>
                    <button class="follow-btn">关注</button>
                </view>

                <!-- 内容区域 - 根据类型展示不同内容 -->
                <view class="content">
                    <!-- 文本内容 -->
                    <view class="content-text" v-if="item.study_title || item.study_content">
                        <view v-if="item.study_title" class="content-title">
                            <rich-text :nodes="item.study_title"></rich-text>
                        </view>
                        <view v-if="item.study_content" class="content-body">
                            <rich-text :nodes="item.study_content"></rich-text>
                        </view>
                    </view>

                    <!-- 话题标签 -->
                    <view class="tags" v-if="item.gambit_name">
                        <text class="tag">#{{ item.gambit_name }}#</text>
                    </view>

                    <!-- 图文内容 -->
                    <view class="image-grid"
                        v-if="(item.study_type === 0 || item.study_type === 3) && item.image_part && item.image_part.length > 0">
                        <!-- 使用计算属性获取网格类名 -->
                        <view class="grid-container" :class="gridClassMap[item.id]">
                            <view class="grid-item" v-for="(img, i) in item.image_part.slice(0, 9)" :key="i">
                                <image :src="img" mode="aspectFill" class="grid-img"></image>
                                <view class="img-count" v-if="item.image_part.length > 9 && i === 8">
                                    +{{ item.image_part.length - 9 }}
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 语音内容 -->
                    <view class="audio-content" v-if="item.study_type === 1">
                        <view class="audio-player">
                            <view class="audio-icon" :class="{ playing: item.is_voice }"
                                @tap="item.is_voice ? stop : play" :data-key="index" :data-vo="item.study_voice">
                                <icon type="sound" size="24" color="#7B61FF"></icon>
                            </view>
                            <view class="audio-wave" :class="{ playing: item.is_voice }">
                                <view class="wave-bar" :style="{ height: '15%', animationDelay: '0ms' }"></view>
                                <view class="wave-bar" :style="{ height: '40%', animationDelay: '100ms' }"></view>
                                <view class="wave-bar" :style="{ height: '25%', animationDelay: '200ms' }"></view>
                                <view class="wave-bar" :style="{ height: '50%', animationDelay: '300ms' }"></view>
                                <view class="wave-bar" :style="{ height: '30%', animationDelay: '400ms' }"></view>
                            </view>
                            <view class="audio-duration">{{ item.starttime }}</view>
                        </view>
                        <image class="audio-cover" :src="item.image_part[0]" mode="aspectFill"
                            v-if="item.image_part && item.image_part.length > 0"></image>
                    </view>

                    <!-- 视频内容 -->
                    <view class="video-content" v-if="item.study_type === 2">
                        <view class="video-container" @tap="home_url" :data-index="index" data-k="3"
                            :data-type="item.study_type" :data-id="item.id">
                            <image v-if="item.image_part && item.image_part.length > 0" :src="item.image_part[0]"
                                mode="aspectFill" class="video-player"></image>
                            <view v-else class="video-player"
                                style="background-color: #000; display: flex; align-items: center; justify-content: center;">
                                <icon type="videofill" size="40" color="white"></icon>
                            </view>
                            <view class="video-play-icon">
                                <icon type="videofill" size="40" color="white"></icon>
                            </view>
                        </view>
                    </view>

                    <!-- 投票内容 -->
                    <view class="poll-content" v-if="item.study_type === 4 || item.study_type === 5">
                        <view class="poll-question">
                            <text v-if="item.study_type === 4">（单选）</text>
                            <text v-if="item.study_type === 5">（多选）</text>
                            {{ item.study_title }}
                        </view>
                        <view class="poll-options">
                            <view class="poll-option" v-for="(option, i) in item.vo" :key="i" v-if="i < 3"
                                @click="dian_option" :data-id="option.id" :data-key="index" :data-index="i">
                                <view class="option-text">{{ option.ballot_name }}</view>
                                <view class="option-stats">
                                    <view class="progress-bar">
                                        <view class="progress-fill"
                                            :style="{ width: option.ratio + '%', backgroundColor: getProgressColor(item.id) }">
                                        </view>
                                    </view>
                                    <view class="percentage">{{ option.ratio }}%</view>
                                    <!-- <view class="vote-count" v-if="item.is_vo_check > 0">{{ option.voters }}</view> -->
                                </view>
                                <view class="selected-icon" v-if="item.vo_id.includes(option.id)">
                                    <icon type="check" size="16" color="#00C853"></icon>
                                </view>
                            </view>
                            <view class="poll-option" v-if="item.vo.length > 3" @tap="home_url" :data-index="index"
                                data-k="3" :data-type="item.study_type" :data-id="item.id">
                                <view class="option-text">查看全部选项</view>
                                <icon type="right" size="16" color="#999"></icon>
                            </view>
                        </view>
                        <view class="poll-total">
                            <view class="poll-stats">
                                <text>参与人数：{{ item.vo_count }}</text>
                                <button v-if="item.vo_id.length > 0 && item.is_vo_check == 0" @tap="vote_do"
                                    :data-index="i" :data-key="index" class="vote-btn">投票</button>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 互动区域 -->
                <view class="actions">
                    <view class="action-item" @click="parseEventDynamicCode($event, 'add_zan')" :data-id="item.id"
                        :data-key="index">
                        <text class="action-emoji" :class="{ 'liked': item.is_info_zan }">
                            {{ item.is_info_zan ? '❤️' : '🤍' }}
                        </text>
                        <text>{{ formatNumber(item.info_zan_count_this) }}</text>
                    </view>
                    <view class="action-item" @click="home_pl" :data-id="item.id" :data-key="index">
                        <text class="action-emoji">💬</text>
                        <text>{{ formatNumber(item.study_repount) }}</text>
                    </view>
                    <view class="action-item">
                        <text class="action-emoji">📤</text>
                        <text>分享</text>
                    </view>
                    <view class="action-item bookmark" @click="toggleBookmark(item.id)">
                        <text class="action-emoji">⭐</text>
                    </view>
                </view>

                <!-- 评论预览 -->
                <view class="comments-preview" v-if="item.reply_list && item.reply_list.length > 0" @tap="home_url"
                    :data-index="index" data-k="3" :data-type="item.study_type" :data-id="item.id">
                    <view class="comment-item" v-for="(comment, i) in item.reply_list" :key="i">
                        <text class="comment-username">{{ comment.user_nick_name }}：</text>
                        <rich-text class="comment-content" :nodes="comment.reply_content"></rich-text>
                        <image v-if="comment.image_part && comment.image_part.length > 0 && comment.image_part[0]!=''" :src="comment.image_part[0]"
                            @tap.stop="previewHuiAndImage" :data-src="comment.image_part[0]" class="comment-image">
                        </image>
                    </view>
                    <view class="view-more-comments" v-if="item.study_repount > item.reply_list.length">
                        查看全部 {{ item.study_repount }} 条评论
                    </view>
                </view>
            </view>

            <!-- 下拉刷新提示 -->
            <view class="refresh-hint" v-if="refreshing">
                <view class="spinner"></view>
                <text>刷新中...</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
        // 计算每个图文内容的网格布局类名，存储在对象中
        gridClassMap() {
            const map = {};
            this.new_list.forEach(item => {
                if ((item.study_type === 0 || item.study_type === 3) && item.image_part) {
                    const count = Math.min(item.image_part.length, 9); // 最多显示9张
                    if (count === 1) {
                        map[item.id] = 'grid-single';
                    } else if (count === 2) {
                        map[item.id] = 'grid-double';
                    } else if (count <= 4) {
                        map[item.id] = 'grid-four';
                    } else if (count <= 6) {
                        map[item.id] = 'grid-six';
                    } else {
                        map[item.id] = 'grid-nine';
                    }
                }
            });
            return map;
        }
    },
    methods: {

        // 格式化数字（大于1000显示k）
        formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num;
        },

        // 获取项目主题色
        getThemeColor(id) {
            // 根据study_type返回对应的主题色
            const item = this.new_list.find(item => item.id === id);
            if (!item) return '#FF4D6D';

            const colorMap = {
                0: '#FF4D6D', // 图文
                1: '#7B61FF', // 语音
                2: '#00C853', // 视频
                3: '#FF9800', // 活动
                4: '#2196F3', // 单选投票
                5: '#2196F3'  // 多选投票
            };
            return colorMap[item.study_type] || '#FF4D6D';
        },

        // 获取进度条颜色
        getProgressColor(id) {
            return this.getThemeColor(id);
        },
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>

<style scoped>
/* 样式保持不变，与上一版本相同 */
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 内容列表 */
.feed-list {
    flex: 1;
    padding: 12px 0;
}

/* 内容卡片 */
.feed-card {
    background-color: white;
    border-radius: 12px;
    margin: 0 16px 12px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.feed-card:active {
    transform: scale(0.995);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
}

/* 内容类型指示条 */
.feed-type-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    border-radius: 2px 0 0 2px;
}

.feed-type-indicator.type-0 {
    background-color: #FF4D6D;
    /* 图文 */
}

.feed-type-indicator.type-1 {
    background-color: #7B61FF;
    /* 语音 */
}

.feed-type-indicator.type-2 {
    background-color: #00C853;
    /* 视频 */
}

.feed-type-indicator.type-3 {
    background-color: #FF9800;
    /* 活动 */
}

.feed-type-indicator.type-4 {
    background-color: #2196F3;
    /* 单选投票 */
}

.feed-type-indicator.type-5 {
    background-color: #2196F3;
    /* 多选投票 */
}

/* 其他样式保持不变 */
.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #f0f0f0;
}

.user-details {
    flex: 1;
}

.username-row {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.username {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-right: 6px;
}

.user-badge {
    font-size: 11px;
    background-color: #f0f0f0;
    color: #666;
    padding: 1px 6px;
    border-radius: 10px;
}

.post-time {
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
}

.visibility {
    font-size: 11px;
}

.follow-btn {
    background-color: #FF4D6D;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
    transition: background-color 0.2s;
}

.follow-btn:active {
    background-color: #e0435f;
}

.followed-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 3px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
}

/* 内容区域 */
.content {
    margin-bottom: 16px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.content-text {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 12px;
    white-space: pre-line;
}

.content-title {
    font-weight: 600;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-body {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 话题标签 */
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.tag {
    font-size: 13px;
    color: #6B7280;
    background-color: #F3F4F6;
    padding: 2px 8px;
    border-radius: 4px;
}

/* 图文内容 */
.image-grid {
    margin-bottom: 12px;
}

.grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.grid-single {
    aspect-ratio: 16/9;
}

.grid-single .grid-item {
    width: 100%;
    height: 100%;
}

.grid-double .grid-item {
    width: calc(50% - 2px);
    aspect-ratio: 1;
}

.grid-multiple .grid-item {
    width: calc(33.333% - 2.66px);
    aspect-ratio: 1;
}

.grid-four .grid-item {
    width: calc(50% - 2px);
    aspect-ratio: 1;
}

.grid-six .grid-item {
    width: calc(33.333% - 2.66px);
    aspect-ratio: 1;
}

.grid-nine .grid-item {
    width: calc(33.333% - 2.66px);
    aspect-ratio: 1;
}

.grid-item {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.grid-img {
    width: 100%;
    height: 100%;
    display: block;
}

.img-count {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px 0 0 0;
}

/* 语音内容 */
.audio-content {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.audio-player {
    flex: 1;
    display: flex;
    align-items: center;
}

.audio-icon {
    margin-right: 12px;
    transition: transform 0.3s;
}

.audio-icon.playing {
    transform: scale(1.1);
}

.audio-wave {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 30px;
    margin-right: 12px;
}

.audio-wave.playing .wave-bar {
    animation: wave 1s infinite ease-in-out;
}

.wave-bar {
    width: 3px;
    background-color: #7B61FF;
    border-radius: 3px;
    transform-origin: center bottom;
}

@keyframes wave {

    0%,
    100% {
        transform: scaleY(0.5);
    }

    50% {
        transform: scaleY(1);
    }
}

.audio-duration {
    font-size: 12px;
    color: #999;
}

.audio-cover {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-left: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 视频内容 */
.video-content {
    margin-bottom: 12px;
}

.video-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background-color: #000;
    cursor: pointer;
}

.video-player {
    width: 100%;
    aspect-ratio: 16/9;
}

.video-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    opacity: 0.8;
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 10;
}

.video-play-count {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 10;
}

/* 活动内容 */
.event-content {
    margin-bottom: 12px;
}

.event-banner-container {
    position: relative;
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
}

.event-banner {
    width: 100%;
    aspect-ratio: 2/1;
    display: block;
}

.event-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #FF9800;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
}

.event-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.event-date,
.event-location {
    display: flex;
    align-items: center;
    gap: 4px;
}

.event-participants {
    display: flex;
    align-items: center;
}

.participant-avatars {
    position: relative;
    height: 26px;
    margin-right: 8px;
}

.participant-avatar {
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.participant-count {
    font-size: 12px;
    color: #999;
}

/* 投票内容 */
.poll-content {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
}

.poll-question {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.4;
}

.poll-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.poll-option {
    background-color: white;
    border-radius: 6px;
    padding: 14px;
    border: 1px solid #eee;
    transition: all 0.2s;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.poll-option.selected {
    border-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.05);
}

.poll-option:active {
    transform: scale(0.99);
}

.option-text {
    font-size: 14px;
    margin-bottom: 8px;
}

.option-stats {
    display: flex;
    align-items: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: #eee;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.percentage {
    font-size: 12px;
    color: #666;
    margin-left: 8px;
    width: 40px;
    text-align: right;
    font-weight: 500;
}

.poll-total {
    font-size: 12px;
    color: #999;
    padding-top: 4px;
}

.poll-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vote-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 16px;
    font-size: 12px;
    height: auto;
    line-height: normal;
}

.vote-count {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #666;
}

.selected-icon {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
}

/* 互动区域 */
.actions {
    display: flex;
    align-items: center;
    border-top: 1px solid #f5f5f5;
    padding-top: 8px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.action-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.action-item:active {
    transform: scale(0.95);
}

.action-item icon {
    margin-right: 4px;
    transition: transform 0.2s;
}

.action-emoji {
    font-size: 18px;
    margin-right: 4px;
    transition: transform 0.2s;
}

.action-item .liked {
    animation: like 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes like {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

.join-btn {
    background-color: #FF9800;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 16px;
    font-size: 14px;
    height: auto;
    line-height: normal;
    margin-left: auto;
    transition: background-color 0.2s;
}

.join-btn:active {
    background-color: #e68900;
}

.bookmark {
    margin-left: auto;
    margin-right: 0;
}

/* 评论预览 */
.comments-preview {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed #f0f0f0;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.comment-item {
    font-size: 14px;
    margin-bottom: 6px;
    line-height: 1.4;
}

.comment-username {
    color: #333;
    font-weight: 500;
}

.comment-content {
    color: #666;
}

.view-more-comments {
    font-size: 13px;
    color: #6B7280;
    margin-top: 4px;
    padding: 4px 0;
}

.view-more-comments:active {
    background-color: #f5f5f5;
    border-radius: 4px;
}

.comment-image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-left: 8px;
    vertical-align: middle;
}

/* 下拉刷新提示 */
.refresh-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #eee;
    border-top-color: #FF4D6D;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>