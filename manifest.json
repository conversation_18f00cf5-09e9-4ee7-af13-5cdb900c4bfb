{"name": "yl_welore", "appid": "__UNI__D04FD01", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "sassImplementationName": "node-sass", "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wx5751e2981a30c314", "setting": {"urlCheck": false, "minified": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于定位效果展示"}}, "requiredPrivateInfos": ["chooseLocation", "<PERSON><PERSON><PERSON><PERSON>", "getLocation"], "plugins": {}, "navigateToMiniProgramAppIdList": [], "requiredBackgroundModes": ["audio"]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "2", "networkTimeout": {"request": 300000, "downloadFile": 300000, "uploadFile": 300000}}