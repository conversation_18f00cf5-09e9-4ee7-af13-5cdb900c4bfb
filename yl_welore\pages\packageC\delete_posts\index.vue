<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">回收站</view>
        </cu-custom>
        <scroll-view scroll-x class="bg-white nav tab-container">
            <view class="flex text-center">
                <view :class="'flex-sub tab-item ' + (current == 'tab1' ? 'cur active-tab' : '')" @tap="handleChange" data-key="tab1">
                    <text class="tab-text">系统删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab2' ? 'cur active-tab' : '')" @tap="handleChange" data-key="tab2">
                    <text class="tab-text">{{ design.qq_name }}主删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab3' ? 'cur active-tab' : '')" @tap="handleChange" data-key="tab3">
                    <text class="tab-text">楼主删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab4' ? 'cur active-tab' : '')" @tap="handleChange" data-key="tab4">
                    <text class="tab-text">自己删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab5' ? 'cur active-tab' : '')" @tap="handleChange" data-key="tab5">
                    <text class="tab-text">系统打回</text>
                </view>
            </view>
        </scroll-view>

        <view style="padding: 0px 20rpx">
            <block v-for="(item, i_index) in info" :key="i_index">
                <view class="post-card" v-if="item.is_reply == 3">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-text">{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view class="reject-reason">
                            <text class="reason-text">审核打回：{{ item.reject_reason }}</text>
                        </view>
                        <view class="post-info">
                            <view class="info-item">
                                <text class="info-text">{{ item.realm_name }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-text">{{ item.prove_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="post-card" v-if="item.is_reply == 0">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-text">{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view class="delete-reason">
                            <text class="reason-text">{{ item.whether_reason }}</text>
                        </view>
                        <block v-for="(is, index) in item.is_complaint_list" :key="index">
                            <view class="complaint-item">
                                <text class="complaint-text">申诉理由：{{ is.tale_content }}</text>
                            </view>
                            <view v-if="is.acceptance_status == 1" class="reply-item">
                                <text class="reply-text">受理回复：{{ is.tale_instruct }}</text>
                            </view>
                        </block>
                        <view class="post-info">
                            <view class="info-item">
                                <text class="info-text">{{ item.realm_name }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-text">{{ item.whetd_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="post-card" v-if="item.is_reply == 1">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-text">帖子：{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view v-if="item.reply_content" class="reply-content">
                            <text class="reply-text">回复：{{ item.reply_content }}</text>
                        </view>
                        <block v-for="(is, index) in item.is_complaint_list" :key="index">
                            <view class="complaint-item">
                                <text class="complaint-text">申诉理由：{{ is.tale_content }}</text>
                            </view>
                            <view v-if="is.acceptance_status == 1" class="reply-item">
                                <text class="reply-text">受理回复：{{ is.tale_instruct }}</text>
                            </view>
                        </block>
                        <view class="post-info">
                            <view class="delete-reason">
                                <text class="reason-text">{{ item.whether_reason }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-text">{{ item.apter_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>
            </block>
        </view>

        <view :class="'cu-load ' + (info.length == 0 ? 'over' : '')"></view>

        <view :class="'cu-modal modal-container ' + (sc_msg ? 'show' : '')">
            <view class="cu-dialog modal-dialog">
                <view class="cu-bar bg-white justify-end modal-header">
                    <view class="content modal-title">
                        <text class="modal-title-text">申诉理由</text>
                    </view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red close-icon"></text>
                    </view>
                </view>
                <view v-if="sc_msg" class="padding-sm modal-body">
                    <textarea @input="is_sc_text" class="modal-textarea" placeholder="请详细填写申诉理由..." />
                </view>
                <view class="cu-bar bg-white justify-end modal-footer">
                    <view class="action">
                        <button class="modal-btn cancel-btn" @tap="hideModal">
                            <text>取消</text>
                        </button>
                        <button class="modal-btn confirm-btn" @tap="do_user_mutter">
                            <text>确定</text>
                        </button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            info: [],
            current: 'tab1',
            page: 1,
            design:{},
            sc_msg: false,
            id: '',
            sc_text: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var design = uni.getStorageSync('is_diy');
        this.design = design;
        //this.get_help_info();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        //this.get_user_banned();
        this.get_user_paper_del();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        handleChange(detail) {
            this.current = detail.currentTarget.dataset.key;
            this.get_user_paper_del();
        },

        /**
         * 获取删帖
         */
        get_user_paper_del() {
            var b = app.globalData.api_root + 'User/get_user_paper_del';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.del_type = this.current;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 申诉
         */
        user_mutter(e) {
            console.log(e);
            this.sc_msg = true;
            this.id = e.currentTarget.dataset.key;
        },

        /**
         *
         */
        is_sc_text(e) {
            this.sc_text = e.detail.value;
        },

        /**
         * 隐藏窗口
         */
        hideModal() {
            this.sc_msg = false;
        },

        /**
         * 申诉
         */
        do_user_mutter() {
            var b = app.globalData.api_root + 'User/do_paper_mutter';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.info[this.id]['id'];
            params.tory_id = this.info[this.id]['tory_id'];
            params.is_reply = this.info[this.id]['is_reply'];
            params.tale_content = this.sc_text;
            if(this.sc_text == ''){
                uni.showToast({
                    title: '请填写申诉理由！',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.sc_msg = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        //this.get_user_banned();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
/* 现代化色彩系统 */
page {
    --primary-color: #4A90E2;
    --secondary-color: #00D4AA;
    --success-color: #52C41A;
    --warning-color: #FAAD14;
    --error-color: #FF4D4F;
    --bg-color: #F8F9FA;
    --card-bg: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #6C757D;
    --text-light: #ADB5BD;
    --border-color: #E9ECEF;
    --shadow-light: rgba(0, 0, 0, 0.08);
    --shadow-medium: rgba(0, 0, 0, 0.12);

    background-color: var(--bg-color);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 现代化标签页样式 */
.tab-container {
    background: var(--card-bg);
    box-shadow: 0 2rpx 16rpx var(--shadow-light);
    border-radius: 0 0 24rpx 24rpx;
}

.tab-item {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 24rpx 16rpx !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16rpx;
    margin: 8rpx 6rpx;
    position: relative;
    min-height: 100rpx;
    background: transparent;
}

.tab-item.active-tab {
    background: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1rpx);
    box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25);
}

.tab-item.active-tab .tab-text {
    color: white !important;
    font-weight: 600;
}

.tab-text {
    font-size: 26rpx !important;
    font-weight: 500;
    text-align: center;
    line-height: 1.3;
    color: var(--text-primary) !important;
    display: block !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140rpx;
    margin-top: 4rpx;
}

/* 现代化帖子卡片样式 */
.post-card {
    background: var(--card-bg);
    border-radius: 20rpx;
    margin: 24rpx 0;
    box-shadow: 0 4rpx 20rpx var(--shadow-light);
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1rpx solid var(--border-color);
}

.post-card:active {
    transform: translateY(1rpx);
    box-shadow: 0 2rpx 12rpx var(--shadow-light);
}

.card-content {
    padding: 32rpx;
}

/* 现代化标题样式 */
.post-title {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid var(--border-color);
    position: relative;
}

.post-title::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1rpx;
    width: 60rpx;
    height: 3rpx;
    background: var(--primary-color);
    border-radius: 2rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.5;
    flex: 1;
}

/* 现代化原因显示样式 */
.reject-reason, .delete-reason {
    display: flex;
    align-items: flex-start;
    margin: 20rpx 0;
    padding: 24rpx;
    background: #FFF2F0;
    border-radius: 16rpx;
    border-left: 4rpx solid var(--error-color);
    position: relative;
}


.reason-text {
    font-size: 28rpx;
    color: var(--error-color);
    line-height: 1.5;
    flex: 1;
    font-weight: 500;
}

/* 现代化回复内容样式 */
.reply-content {
    display: flex;
    align-items: flex-start;
    margin: 20rpx 0;
    padding: 24rpx;
    background: #F0F9FF;
    border-radius: 16rpx;
    border-left: 4rpx solid var(--primary-color);
    position: relative;
}

.reply-content::before {
    content: '💬';
    position: absolute;
    left: 24rpx;
    top: 24rpx;
    font-size: 24rpx;
    color: var(--primary-color);
}

.reply-text {
    font-size: 28rpx;
    color: var(--primary-color);
    line-height: 1.5;
    flex: 1;
    margin-left: 40rpx;
    font-weight: 500;
}

/* 现代化申诉项目样式 */
.complaint-item {
    display: flex;
    align-items: flex-start;
    margin: 16rpx 0;
    padding: 24rpx;
    background: #FFFBF0;
    border-radius: 16rpx;
    border-left: 4rpx solid var(--warning-color);
    position: relative;
}

.complaint-text {
    font-size: 28rpx;
    color: var(--warning-color);
    line-height: 1.5;
    flex: 1;
    font-weight: 500;
}

/* 现代化回复项目样式 */
.reply-item {
    display: flex;
    align-items: flex-start;
    margin: 16rpx 0;
    padding: 24rpx;
    background: #F6FFED;
    border-radius: 16rpx;
    border-left: 4rpx solid var(--success-color);
    position: relative;
}

.reply-item::before {
    content: '✅';
    position: absolute;
    left: 24rpx;
    top: 24rpx;
    font-size: 24rpx;
    color: var(--success-color);
}

.reply-item .reply-text {
    font-size: 28rpx;
    color: var(--success-color);
    line-height: 1.5;
    flex: 1;
    margin-left: 40rpx;
    font-weight: 500;
}

/* 现代化信息项样式 */
.post-info {
    margin-top: 28rpx;
    padding-top: 24rpx;
    border-top: 1rpx solid var(--border-color);
}

.info-item {
    display: flex;
    align-items: center;
    margin: 16rpx 0;
    padding: 8rpx 0;
}

.info-item::before {
    content: '';
    width: 8rpx;
    height: 8rpx;
    background: var(--text-light);
    border-radius: 50%;
    margin-right: 16rpx;
    flex-shrink: 0;
}

.info-text {
    font-size: 26rpx;
    color: var(--text-secondary);
    line-height: 1.4;
    font-weight: 500;
}

/* 现代化操作按钮样式 */
.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx 32rpx;
    border-radius: 24rpx;
    margin: 20rpx 0;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    font-weight: 600;
    font-size: 26rpx;
    border: none;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.action-btn:active::before {
    transform: translateX(0);
}

.restore-btn {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25);
}

.restore-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.appealed-btn {
    background: var(--warning-color);
    color: white;
    box-shadow: 0 8rpx 24rpx rgba(250, 173, 20, 0.25);
}

.appealed-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
}

.btn-text {
    font-size: 26rpx;
    font-weight: 600;
}

/* 现代化模态框样式 */
.modal-container {
    backdrop-filter: blur(12rpx);
    background: rgba(0, 0, 0, 0.4);
}

.modal-dialog {
    border-radius: 28rpx;
    overflow: hidden;
    box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.25);
    border: 1rpx solid var(--border-color);
    background: var(--card-bg);
}

.modal-header {
    background: var(--primary-color);
    color: white;
    padding: 32rpx 40rpx;
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: rgba(255, 255, 255, 0.1);
}

.modal-title {
    display: flex;
    align-items: center;
}

.modal-title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: white;
}

.close-icon {
    font-size: 36rpx;
    color: white;
    opacity: 0.8;
    transition: all 0.25s ease;
    padding: 8rpx;
    border-radius: 8rpx;
}

.close-icon:active {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 40rpx;
    background: var(--card-bg);
}

.modal-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 24rpx;
    border: 2rpx solid var(--border-color);
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    background: var(--card-bg);
    transition: all 0.25s ease;
    box-sizing: border-box;
    color: var(--text-primary);
    resize: none;
}

.modal-textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
}

.modal-footer {
    padding: 32rpx 40rpx;
    background: var(--card-bg);
    border-top: 1rpx solid var(--border-color);
}

.modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 24rpx;
    padding: 20rpx 40rpx;
    font-weight: 600;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    height: 88rpx;
    margin-left: 24rpx;
    font-size: 28rpx;
    border: none;
    position: relative;
    overflow: hidden;
}

.modal-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.modal-btn:active::before {
    transform: translateX(0);
}

.cancel-btn {
    border: 2rpx solid var(--text-light);
    color: var(--text-secondary);
    background: transparent;
}

.cancel-btn:active {
    background: var(--text-light);
    color: white;
    border-color: var(--text-light);
}

.confirm-btn {
    background: var(--success-color);
    color: white;
    box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.25);
}

.confirm-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

/* 现代化加载状态 */
.cu-load.over::before {
    content: "已加载全部内容";
    color: var(--text-light);
    font-size: 28rpx;
    font-weight: 500;
}

/* 现代化响应式设计 */
@media (max-width: 750rpx) {
    .tab-text {
        font-size: 24rpx !important;
        max-width: 120rpx;
    }

    .tab-item {
        min-height: 90rpx;
        padding: 20rpx 12rpx !important;
        margin: 6rpx 4rpx;
    }

    .post-card {
        margin: 20rpx 0;
        border-radius: 16rpx;
    }

    .card-content {
        padding: 28rpx;
    }

    .modal-dialog {
        margin: 40rpx 20rpx;
        border-radius: 24rpx;
    }

    .modal-body {
        padding: 32rpx;
    }

    .modal-footer {
        padding: 24rpx 32rpx;
    }

    .modal-btn {
        height: 80rpx;
        font-size: 26rpx;
        margin-left: 16rpx;
    }
}

/* 现代化动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(24rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.post-card {
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-item {
    animation: fadeIn 0.3s ease-out;
}

.modal-dialog {
    animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 微交互动画 */
.action-btn, .modal-btn {
    transform-origin: center;
}

.action-btn:active, .modal-btn:active {
    animation: buttonPress 0.1s ease-out;
}

@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}
</style>
