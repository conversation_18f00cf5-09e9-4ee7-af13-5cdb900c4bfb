<template>
  <view>
    <cu-custom bgColor="welfare_top_color" :isSearch="false" :isBack="true" TextColor="rgb(126,110,255)">
      <view slot="backText"></view>
      <view slot="content" style="color: #fff;font-weight: 600; font-size: 36rpx;">{{ info.er_name }}</view>
    </cu-custom>
    <view class="page">
      <view style="width:100%;margin: 0px auto;">
        <view
          style="background-image: linear-gradient(to right, #f83600 0%, #f9d423 100%);margin:0px 5px;border-radius: 5px;">
          <view class="grid col-2 text-center" style="color: #fff;">
            <view class="cu-list menu-avatar">
              <view class="cu-item" style="background-color: transparent;margin-top: 15px;">
                <view class="cu-avatar round lg"
                  :style="'background-image:url(' + (user_info.user_head_sculpture) + ');width: 120rpx;height: 120rpx;'">
                </view>
                <view class="content flex-sub" style="top: 5px;left: 84px;">
                  <view style="margin:5px 0px 10px 0px">{{ user_info.user_nick_name }}</view>
                  <view class="text-sm flex">
                    <image class="now_level" :src="user_info.level_info.level_icon" mode="heightFix"
                      style="height:40rpx;vertical-align: sub;"></image>
                    <text style="flex:1;">{{ user_info.level_info.level_name }}</text>
                  </view>
                </view>
              </view>
              <button v-if="flag == false" @tap="bid_qiandao"
                style="color:#fff;margin: 0 auto;width: 80%;margin-top: 23px;" class="cu-btn round bg-black"
                role="button" :aria-disabled="false">点击签到</button>
              <button v-if="flag" style="margin: 0 auto;width: 80%;margin-top: 23px;" class="cu-btn round bg-black"
                role="button" :disabled="true">已经签到</button>
            </view>
            <view class="grid col-1 align-center">
              <view class="cu-list menu sm-border">
                <view class="cu-item" style="background-color: transparent;padding:0px;">
                  <view class="content" style="flex:none;">
                    <text style="margin-right:0px;font-size: 18px;color: #FFCC00;vertical-align: sub;"
                      class="cuIcon-rechargefill text-gray"></text>
                    <text style="margin:0px 5px 0px 0px;vertical-align: sub;">{{ $state.diy.confer }}</text>
                    <text style="vertical-align: sub;font-size:20px;">{{ user_info.fraction }}</text>
                  </view>
                </view>
                <view class="cu-item" style="background-color: transparent;padding:0px;">
                  <view class="content" style="flex:none;">
                    <text style="margin-right:0px;font-size: 18px;color: #FFCC00;vertical-align: sub;"
                      class="cuIcon-choicenessfill text-gray"></text>
                    <text style="margin:0px 5px 0px 0px;vertical-align: sub;">{{ $state.diy.currency }}</text>
                    <text style="vertical-align: sub;font-size:20px;">{{ user_info.conch }}</text>
                  </view>
                </view>
                <view class="cu-item" style="background-color: transparent;padding:0px;">
                  <view class="content" style="flex:none;">
                    <text style="margin-right:0px;font-size: 18px;color: #FFCC00;vertical-align: sub;"
                      class="cuIcon-crownfill text-gray"></text>
                    <text style="margin:0px 5px 0px 0px;vertical-align: sub;">荣誉</text>
                    <text style="vertical-align: sub;font-size:20px;">{{ user_info.honor_point }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="back_back"
        :style="'background-image: url(' + (http_root) + 'addons/yl_welore/web/static/wechat/machine-bg.png);'">
        <view style="text-align: center;position:relative;">
          <image mode="widthFix" style="width:230px;height:156.4px;margin-top: 65px;"
            :src="(http_root) + 'addons/yl_welore/web/static/wechat/xydcj.png'"></image>
          <view v-if="info.draw_restrictions != 0" style="letter-spacing: 1.5px;color:#000;margin: 20px 0px;">今天还剩
            <text style="font-size: 20px;color: #fff;margin: 0px 5px;">{{ sheng }}</text>次抽奖机会
          </view>
          <view @tap="user_url" data-key="1"
            style="position: absolute;right: 0px;bottom: 10%;background-color: #FF9999;color: #fff;padding: 3px 10px;border-radius: 20px 0px 0px 20px;">
            活动规则</view>
          <view @tap="user_url" data-key="2"
            style="position: absolute;right: 0px;bottom: -10%;background-color: #FFCC99;color: #fff;padding: 3px 10px;border-radius: 20px 0px 0px 20px;">
            获奖记录</view>
        </view>
        <view class="turntable-area">
          <view class="turntable">
            <view class="dot" v-for="(item, index) in (6)" :key="index">



              <text class="yl_vip_a" v-for="(item, index1) in (6)" :key="index1"></text>



            </view>
            <view class="prize">
              <view class="p-item">
                <view class="pi-item" :style="'opacity:' + (info.prize_content[index].opa)" v-for="(item, index) in (3)"
                  :key="index">



                  <view class="p-info">
                    <image class="p-cover" mode="aspectFit" :src="info.prize_content[index].prize_img"></image>
                    <view class="p-name">{{ info.prize_content[index].prize_name }}</view>
                  </view>



                </view>
              </view>
              <view class="p-item">
                <view class="pi-item" :style="'opacity:' + (info.prize_content[7].opa)">
                  <view class="p-info">
                    <image class="p-cover" mode="aspectFit" :src="info.prize_content[7].prize_img"></image>
                    <view class="p-name">{{ info.prize_content[7].prize_name }}</view>
                  </view>
                </view>
                <view :class="'pi-item ' + (isDisabled ? 'btn-lottery-disabled' : 'btn-lottery')" @tap="check_luck">
                  <view class="p-info">
                    <view v-if="cha > 0" class="p-name">免费抽奖</view>
                    <view v-if="cha <= 0" class="p-name">
                      <view> {{ info.deplete_score }}{{ info.deplete_type == 0 ? $state.diy.currency : $state.diy.confer
                      }}</view>
                      <view>抽一次</view>
                    </view>
                  </view>
                </view>
                <view class="pi-item" :style="'opacity:' + (info.prize_content[3].opa)">
                  <view class="p-info">
                    <image class="p-cover" mode="aspectFit" :src="info.prize_content[3].prize_img"></image>
                    <view class="p-name">{{ info.prize_content[3].prize_name }}</view>
                  </view>
                </view>
              </view>
              <view class="p-item">
                <view class="pi-item"
                  :style="'opacity:' + (info.prize_content[index === 0 ? 6 : index === 1 ? 5 : 4].opa)"
                  v-for="(item, index) in (3)" :key="index">



                  <view class="p-info">
                    <image class="p-cover" mode="aspectFit"
                      :src="info.prize_content[index === 0 ? 6 : index === 1 ? 5 : 4].prize_img"></image>
                    <view class="p-name">{{ info.prize_content[index === 0 ? 6 : index === 1 ? 5 : 4].prize_name }}
                    </view>
                  </view>



                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="award-back">
          <view class="award-title">中奖幸运达人</view>
          <view style="text-align: center;" v-if="list.length == 0">暂无动态</view>
          <swiper class="prize-list" :autoplay="list.length < 5 ? 'false' : 'true'" :vertical="true" :circular="true"
            interval="3000" :display-multiple-items="list.length > 5 ? 5 : list.length">
            <swiper-item @touchmove.stop.prevent class="prize-item" v-for="(item, index) in (list)" :key="index">



              <view style="display:flex;">
                <view style="flex: 1;">
                  <text style="font-size:13px;">{{ item.user_nick_name }}</text>
                </view>
                <view style="flex: 1;">
                  <text style="font-size:15px;margin: 0px 15px;color: #fff;">{{ item.prize_name }}</text>
                </view>
                <view style="flex: 1;">
                  <text style="font-size:13px;">{{ item.join_time }} </text>
                </view>
              </view>






            </swiper-item>
          </swiper>
        </view>
      </view>

    </view>


    <view :class="'cu-modal ' + (modalName == true ? 'show' : '')">
      <view class="cu-dialog" style="border-radius:0px;background-color:transparent;">
        <view style="position:relative;">
          <image style="height: 180.2px;" src="/static/yl_welore/style/icon/zAPP_S.png"></image>
          <view style="position:absolute;left: 0px;right: 0px;top: 35%;font-size: 16px;z-index: 1;">
            {{ luck_info.prize_name }}</view>
          <view style="position:absolute;left: 0px;right: 0px;top: 56%;">
            <image style="width:50px;height:50px;" :src="luck_info.prize_img"></image>
          </view>
        </view>
        <view
          style="border-radius: 5px;width: 100px;height: 40px;margin: 0 auto;background-color: #FF3300;color: #fff;">
          <view style="line-height: 40px;" class="action margin-0  solid-left" @tap="hideModal_shou">收入囊中</view>
        </view>
      </view>
    </view>

    <view :class="'cu-modal ' + (get_tishi == true ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">提示</view>
          <view class="action" @tap="hideModal_shou">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="padding-xl">
          {{ get_msg }}
        </view>
        <view class="cu-bar bg-white justify-end">
          <view class="action">
            <button v-if="info.free_ad_valve == 1" class="cu-btn line-green text-green" @tap="get_viedo">看广告抽一次</button>
            <button style="border-radius:5px;" class="cu-btn bg-green margin-left" @tap="startLucks">{{
              info.deplete_type == 0 ? $state.diy.currency : $state.diy.confer }}抽奖</button>

          </view>
        </view>
      </view>
    </view>
    <login id="login" @checkPhoen="check_user_login = false;" :check_user_login="check_user_login"></login>
    <phone id="phone" @close_phone_modal="check_phone_show = false;" :check_phone="check_phone_show"></phone>
  </view>
</template>

<script>
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
const app = getApp();
const http = require("../../../util/http.js");

// 计数器
let interval = null;
// 值越大旋转时间约长
let intime = 100;

export default {
  components: {
    login,
    phone,
  },
  /**
   * 页面的初始数据
   */
  data() {
    return {
      check_phone_show: false,
      check_user_login: false,
      http_root: app.globalData.http_root,
      info: {},
      list: [],
      luckPosition: 1,
      isDisabled: false,
      count: 0,
      error_code: 0,
      modalName: false,
      luck_info: {},
      user_info: {},
      cha: 0,
      sheng: 0,
      flag: true,
      originalHeight: 0,
      originalWidth: 0,
      get_tishi: false,
      get_msg: ''
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    uni.showLoading({
      title: '奖品设置中...'
    });

    if (uni.createRewardedVideoAd) {
      const rewardedVideoAd = uni.createRewardedVideoAd({
        adUnitId: getApp().globalData.store.getState().copyright['jili']
      });
      rewardedVideoAd.onLoad(() => {
        console.log('onLoad event emit');
      });
      rewardedVideoAd.onError(err => {
        console.log('onError event emit', err);
      });
      rewardedVideoAd.onClose(res => {
        if (res && res.isEnded) {
          this.startLucks(1);
        }
      });
      this.rewardedVideoAd = rewardedVideoAd;
    }
    this.doIt();
  },
  onShow() { },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    //清空计时器
    clearInterval(interval);
  },
  methods: {
    doIt() {
      app.globalData.getLogin(
        // 成功回调 returnA 
        (userInfo) => {
          console.log(' 登录成功:', userInfo);
          this.get_raffle();
          this.get_user_info();
          var over_time = app.globalData.getCache("over_time");
          var dd = wx.getStorageSync('is_diy');
          console.log(dd);
          var this_time = parseInt(+new Date / 1000);
          if (!dd) {
            this.get_diy();
          }
        },
        // 失败回调 returnB 
        (err) => {
          console.error(' 登录失败:', err);
        }
      );
    },
    get_diy() {
      const b = app.globalData.api_root + 'User/get_diy';
      const e = app.globalData.getCache("userinfo");
      const params = {
        token: e.token,
        openid: e.openid
      };
      http.POST(b, {
        params: params,
        success: (res) => {
          if (res.data.status) {
            return;
          } else {
            app.globalData.store.setState({
              diy: res.data
            });
            const n = parseInt(+new Date() / 1000) + 129600;
            uni.setStorageSync("is_diy", res.data);
            app.globalData.setCache('over_time', n);
          }
        },
        fail: (err) => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
          });
          reject(err);
        }
      });
    },
    checkToken() {
      return new Promise((resolve) => {
        const e = app.globalData.getCache("userinfo");
        if (!e) {
          resolve({ data: { status: 'no' } });
          return;
        }
        const b = app.globalData.api_root + 'Check/check_token';
        const params = {
          token: e.token,
          openid: e.openid
        };
        http.POST(b, {
          params: params,
          success: (res) => {
            resolve(res);
          },
          fail: (err) => {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
            });
            resolve({ data: { status: 'no' } }); // Resolve with failure status
          }
        });
      });
    },
    /**
     * 登陆
     */
    _getLogin() {
      return new Promise((resolve, reject) => {
        uni.login({
          success: async (res) => {
            try {
              const open = await this._http(app.globalData.api_root + 'Login/index', { code: res.code });
              if (open.data.code !== 0) {
                uni.showModal({
                  title: '系统提示',
                  content: '您设置的小程序参数有误，请自行检查！'
                });
                return reject(new Error('小程序参数有误'));
              }
              const touristInfo = await this._http(app.globalData.api_root + 'Login/add_tourist', {
                openid: open.data.info.openid,
                session_key: open.data.info.session_key,
              });
              app.globalData.setCache("userinfo", touristInfo.data.info);
              resolve(touristInfo);
            } catch (error) {
              reject(error);
            }
          },
          fail: reject
        });
      });
    },
    get_viedo() {
      this.get_tishi = false;
      if (this.rewardedVideoAd) {
        this.rewardedVideoAd.show().catch(() => {
          this.rewardedVideoAd.load().then(() => this.rewardedVideoAd.show()).catch(err => {
            uni.showModal({ title: '提示', content: '准备广告中，请稍后重试', showCancel: false });
          });
        });
      }
    },
    hideModal_shou() {
      this.get_tishi = false;
      this.modalName = false;
      this.isDisabled = false;
      this.get_user_info();
      this.get_raffle();
    },
    bid_qiandao() {
      const e = app.globalData.getCache("userinfo");
      if (e.tourist == 1) {
        uni.showToast({
          title: '请登录！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.vibrateShort();
      this.user_punch();
    },
    /**
     * 签到
     */
    async user_punch() {
      this.flag = true;
      const b = app.globalData.api_root + 'User/add_user_punch';
      const e = app.globalData.getCache("userinfo");
      const params = {
        token: e.token,
        openid: e.openid,
        uid: e.uid
      };
      try {
        const res = await this._http(b, params);
        if (res.data.status == 'success') {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000
          });
          this.get_user_info();
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000
          });
          this.flag = false;
        }
      } catch (err) {
        this.flag = false;
        uni.showModal({
          title: '提示',
          content: '网络繁忙，请稍候重试！',
          showCancel: false
        });
      }
    },
    //获取用户信息
    async get_user_info() {
      const b = app.globalData.api_root + 'User/get_user_info';
      const e = app.globalData.getCache("userinfo");
      const params = {
        token: e.token,
        openid: e.openid,
      };
      try {
        const res = await this._http(b, params);
        if (res.data.status == 'success') {
          this.user_info = res.data.info;
          this.flag = res.data.info.is_sign == 1 ? true : false;
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000
          });
        }
        return res;
      } catch (err) {
        uni.showModal({
          title: '提示',
          content: '网络繁忙，请稍候重试！',
          showCancel: false
        });
        throw err;
      }
    },
    user_url(d) {
      const key = d.currentTarget.dataset.key;
      if (key == 1) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/activity_rules/index'
        });
      }
      if (key == 2) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/user_luck/index'
        });
      }
    },
    hideModal() {
      this.modalName = false;
      this.isDisabled = false;
      this.loadAnimation();
    },
    check_luck() {
      var checkLogin = app.globalData.checkPhoneLogin(1);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      this.get_user_info();
      if (this.isDisabled) {
        return;
      }
      if (this.info['prize_count'] == 0) {
        uni.showModal({
          title: '提示',
          content: '奖品已抽完，正在补货中...',
          showCancel: false
        });
        return;
      }
      if (this.error_code == 2) {
        uni.showModal({
          title: '提示',
          content: '抽奖活动还没有开始哦!',
          showCancel: false
        });
        return;
      }
      if (this.error_code == 3) {
        uni.showModal({
          title: '提示',
          content: '抽奖活动已经结束了!',
          showCancel: false
        });
        return;
      }
      console.log(this.cha);
      console.log(this.info['draw_restrictions']);
      if (this.cha <= 0) {
        if (this.info['deplete_type'] == 1 && this.info['free_ad_valve'] == 0) {
          if (parseFloat(this.user_info['fraction']) - parseInt(this.info['deplete_score']) < 0) {
            uni.showModal({
              title: '提示',
              content: '余额不足，无法抽奖！',
              showCancel: false
            });
            return;
          }
        }
        if (this.info['deplete_type'] == 0 && this.info['free_ad_valve'] == 0) {
          if (parseFloat(this.user_info['conch']) - parseInt(this.info['deplete_score']) < 0) {
            uni.showModal({
              title: '提示',
              content: '余额不足，无法抽奖！',
              showCancel: false
            });
            return;
          }
        }
      }
      const confer = getApp().globalData.store.$state.diy['confer'];
      const currency = getApp().globalData.store.$state.diy['currency'];
      let msg = '';
      if (this.info['deplete_type'] == 1) {
        msg = this.info['deplete_score'] + confer + '抽一次吗？\n';
      } else {
        msg = this.info['deplete_score'] + currency + '抽一次吗？\n';
      }
      if (this.info['draw_restrictions'] != 0) {
        if (this.sheng > 0) {
          if (this.cha <= 0) {
            this.get_tishi = true,
              this.get_msg = msg
            return;
          }
        } else {
          uni.showModal({
            title: '提示',
            content: '今日抽奖已达上限！',
            showCancel: false
          });
          return;
        }
      } else {
        if (this.cha <= 0) {
          this.get_tishi = true;
          this.get_msg = msg;
          return;
        }
      }
      this.startLucks(0);
    },
    //点击抽奖按钮
    startLucks(type) {
      let {
        luckPosition,
        isDisabled
      } = this;
      let list = this.info.prize_content;
      if (isDisabled) {
        return;
      }
      if (!isDisabled) {
        //判断中奖位置格式
        if (luckPosition == null || isNaN(luckPosition) || luckPosition > 7) {
          uni.showModal({
            title: '提示',
            content: '系统发生错误，请稍后重试',
            showCancel: false
          });
          return;
        }

        //设置按钮不可点击
        this.isDisabled = true;
        this.get_tishi = false;

        //清空计时器
        clearInterval(interval);
        let index = 0;
        //循环设置每一项的透明度
        interval = setInterval(() => {
          if (index > 7) {
            index = 0;
            list[7].opa = 0.9;
          } else if (index != 0) {
            list[index - 1].opa = 0.9;
          }
          list[index].opa = 1;
          index++;
          this.info.prize_content = [].concat(list);
        }, intime);
        //模拟网络请求时间  设为两秒
        const stoptime = 2000;
        setTimeout(() => {
          this.set_user_records(type);
        }, stoptime);
      }
    },
    stop(which) {
      let list = this.info.prize_content;

      //清空计数器
      clearInterval(interval);
      //初始化当前位置
      let current = -1;
      for (let i = 0; i < list.length; i++) {
        if (list[i].opa == 1) {
          current = i;
        }
      }
      //下标从1开始
      let index = current + 1;
      this.stopLuck(which, index, intime, 10);
    },
    stopLuck(which, index, time, splittime) {
      let list = this.info.prize_content;
      //值越大出现中奖结果后减速时间越长
      setTimeout(() => {
        //重置前一个位置
        if (index > 7) {
          index = 0;
          list[7].opa = 0.9;
        } else if (index != 0) {
          list[index - 1].opa = 0.9;
        }
        //当前位置为选中状态
        list[index].opa = 1;
        this.info.prize_content = [].concat(list);
        //如果旋转时间过短或者当前位置不等于中奖位置则递归执行
        //直到旋转至中奖位置
        if (time < 400 || index != which) {
          //越来越慢
          splittime++;
          time += splittime;
          //当前位置+1
          index++;
          this.stopLuck(which, index, time, splittime);
        } else {
          //1秒后显示弹窗
          setTimeout(() => {
            console.log(which);
            //this.get_math();
            if (list[which].choose != 0) {
              //中奖
              this.modalName = true;
              this.luck_info = list[this.luckPosition];
            } else {
              //未中奖
              uni.showModal({
                content: '很遗憾未中奖',
                showCancel: false,
                confirmColor: "#F8C219",
                success: res => {
                  if (res.confirm) {
                    //设置按钮可以点击
                    this.hideModal();
                  }
                }
              });
            }
          }, 1000);
        }
      }, time);
    },
    loadAnimation() {
      let index = 0;
      let list = this.info.prize_content;
      clearInterval(interval);
      interval = setInterval(() => {
        if (index > 7) {
          index = 0;
          list[7].opa = 0.9;
        } else if (index != 0) {
          list[index - 1].opa = 0.9;
        }
        list[index].opa = 1;
        index++;
        this.info.prize_content = [].concat(list);
      }, 1000);
    },

    _http(url, params) {
      return new Promise((resolve, reject) => {
        http.POST(url, {
          params,
          success: (res) => resolve(res),
          fail: (err) => {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
            });
            reject(err);
          },
        });
      });
    },
    //获取活动
    async get_raffle() {
      const b = app.globalData.api_root + 'Subscribe/get_raffle';
      const e = app.globalData.getCache("userinfo");
      const params = {
        token: e.token,
        openid: e.openid
      };

      try {
        const res = await this._http(b, params);
        if (res.data.status == 'success') {
          this.info = res.data.info;
          this.list = res.data.list;
          this.count = res.data.count;
          this.cha = res.data.info['free_chance'] - res.data.count;
          this.sheng = res.data.info['draw_restrictions'] - res.data.count;
          this.error_code = res.data.error_code;
          intime = res.data.info.turning_speed;
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000
          });
        }
        this.hideModal();
        uni.hideLoading();
        return res;
      } catch (err) {
        uni.showModal({
          title: '提示',
          content: '网络繁忙，请稍候重试！',
          showCancel: false,
        });
        uni.hideLoading();
        throw err;
      }
    },
    //抽
    async set_user_records(type) {
      const b = app.globalData.api_root + 'Subscribe/set_user_records';
      const e = app.globalData.getCache("userinfo");
      const params = {
        token: e.token,
        openid: e.openid,
        type: type,
      };

      try {
        const res = await this._http(b, params);
        if (res.data.status == 'success') {
          this.stop(res.data.id);
          this.count = res.data.count + 1;
          this.luckPosition = res.data.id;
          this.cha = this.info['free_chance'] - (res.data.count + 1);
          this.sheng = this.info['draw_restrictions'] - (res.data.count + 1);
        } else {
          this.hideModal();
          if (res.data.error_code == -1) {
            uni.showModal({
              title: '提示',
              confirmText: '去钱包',
              content: res.data.msg,
              cancelText: '确定',
              success: function (res) {
                if (res.confirm) {
                  uni.navigateTo({
                    url: '/yl_welore/pages/packageC/user_details/index'
                  });
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false
            });
          }
        }
        uni.hideLoading();
      } catch (err) {
        uni.hideLoading();
        this.hideModal();
        uni.showModal({
          title: '提示',
          content: '网络繁忙，请稍候重试！',
          showCancel: false
        });
      }
    }
  }
};
</script>
<style scoped>
.back_back {
  background-size: 100%;
  background-repeat: no-repeat;
  padding-bottom: 30px;
  margin: 5px 5px 0px 5px;
  border-radius: 5px 5px 0px 0px;
}

.top_color {
  position: static !important;
}

.page {
  display: block;
  min-height: 100%;
  width: 100%;
  /* background-image: linear-gradient(to top, #c471f5 0%, #fa71cd 100%); */
  background-image: linear-gradient(to top, #b224ef 0%, #7579ff 100%);
}

.get_border {
  border: 1px #ee626c solid;
}

.turntable-area {
  margin-top: 55px;
}

.turntable-area .turntable {
  width: 620rpx;
  height: 620rpx;
  border-radius: 15rpx;
  background: #ee626c;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
  margin: 0 auto;
}

.turntable-area .turntable .dot {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 0;
  flex-direction: column;
  height: 100%;
}

.turntable-area .turntable .dot text {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  box-shadow: 0px 1rpx 1rpx 0px rgba(255, 114, 0, 0.75);
  background: #fff;
}

.turntable-area .turntable .dot:nth-child(2n+1) text {
  background: #fdf14f;
}

.turntable-area .turntable .dot text:nth-child(2n+1) {
  background: #fdf14f;
}

.turntable-area .turntable .dot:nth-child(2n+1) text:nth-child(2n+1) {
  background: #fff;
}

.turntable-area .turntable .prize {
  position: absolute;
  top: 38rpx;
  left: 38rpx;
  width: 545rpx;
  height: 545rpx;
  border-radius: 5px;
  background: #f03;
}

.p-item {
  display: flex;
  align-items: center;
  margin: 4rpx 6rpx 0 6rpx;
  flex-wrap: wrap;
}

.p-item .pi-item {
  flex: 1;
  display: flex;
  height: 176rpx;
  background: #ffe1df;
  border-radius: 10rpx;
  width: 33.33%;
}

.p-item .pi-item+.pi-item {
  margin-left: 4rpx;
}

.p-info {
  background-color: #fff;
  height: 176rpx;
  width: 100%;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.p-info .p-cover {
  width: 102rpx;
  height: 71rpx;
}

.p-info .p-name {
  font-size: 13px;
  text-align: center;
}

/**
*动态
*/

.prize-list {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.prize-item {
  font-size: 15px;
  height: 40px;
  line-height: 40px;
  text-align: center;
}

.award-back {
  padding: 30px 10px;
}

.award-title {
  color: #fff;
}

/* .cu-bar.fixed, .nav.fixed {
  position: relative !important;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: none !important;
} */

.right_top {
  font-size: 12px;
}

.right_bottom {
  font-size: 18px;
  margin-left: 3px;
}
</style>