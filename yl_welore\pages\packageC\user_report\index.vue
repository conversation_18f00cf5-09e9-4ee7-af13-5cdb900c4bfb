<template>
    <view class="report-container">
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">举报投诉记录</view>
        </cu-custom>

        <!-- 简洁美观的标签页 -->
        <view class="tab-container">
            <view class="tab-wrapper">
                <view
                    :class="'tab-item ' + (current == 'tab1' ? 'active' : '')"
                    @tap="handleChange"
                    data-key="tab1"
                >
                    <text class="tab-text">帖子</text>
                </view>
                <view
                    :class="'tab-item ' + (current == 'tab2' ? 'active' : '')"
                    @tap="handleChange"
                    data-key="tab2"
                >
                    <text class="tab-text">回复</text>
                </view>
            </view>
            <view class="tab-indicator" :class="'indicator-' + current"></view>
        </view>

        <!-- 优化后的内容区域 -->
        <view class="content-container">
            <view v-for="(item, index) in info_list" :key="index">
                <!-- 帖子类型 -->
                <view class="report-card" v-if="item.tale_type == 0">
                    <view class="card-header">
                        <view class="type-badge post-badge">
                            <text class="badge-text">帖子举报</text>
                        </view>
                        <view class="status-badge" :class="item.paper.whether_type != 0 ? 'status-rejected' : (item.paper.study_status == 2 ? 'status-returned' : 'status-processing')">
                            <text class="status-icon">{{ getStatusIcon(item) }}</text>
                            <text class="status-text">{{ getStatusText(item) }}</text>
                        </view>
                    </view>

                    <view class="card-content">
                        <view class="content-title">
                            {{ item.paper.study_title == '' ? item.paper.study_content : item.paper.study_title }}
                        </view>
                        <view class="content-description">
                            <text class="desc-text">{{ item.tale_content }}</text>
                        </view>
                    </view>

                    <view class="card-footer">
                        <view class="footer-item">
                            <text class="footer-icon">🏷️</text>
                            <text class="footer-text">{{ item.realm_name }}</text>
                        </view>
                        <view class="footer-item">
                            <text class="footer-icon">⏰</text>
                            <text class="footer-text">{{ item.petition_time }}</text>
                        </view>
                    </view>

                    <view class="reason-section" v-if="item.paper.whether_type != 0 || (item.paper.whether_type == 0 && item.paper.study_status == 2)">
                        <view class="reason-content">
                            <text class="reason-icon">⚠️</text>
                            <text class="reason-text">
                                {{ item.paper.whether_type != 0 ? item.paper.whether_reason : '帖子被打回' }}
                            </text>
                        </view>
                    </view>
                </view>

                <!-- 回复类型 -->
                <view class="report-card" v-if="item.tale_type == 1">
                    <view class="card-header">
                        <view class="type-badge reply-badge">
                            <text class="badge-icon">💬</text>
                            <text class="badge-text">回复举报</text>
                        </view>
                        <view class="status-badge" :class="item.paper.whether_type != 0 ? 'status-rejected' : (item.acceptance_status == 0 ? 'status-checking' : 'status-processing')">
                            <text class="status-icon">{{ getReplyStatusIcon(item) }}</text>
                            <text class="status-text">{{ getReplyStatusText(item) }}</text>
                        </view>
                    </view>

                    <view class="card-content">
                        <view class="content-title">
                            {{ item.paper.study_title == '' ? item.paper.study_content : item.paper.study_title }}
                        </view>
                        <view class="content-description">
                            <text class="desc-icon">📄</text>
                            <text class="desc-text">{{ item.tale_content }}</text>
                        </view>
                    </view>

                    <view class="card-footer">
                        <view class="footer-item">
                            <text class="footer-icon">🏷️</text>
                            <text class="footer-text">{{ item.realm_name }}</text>
                        </view>
                        <view class="footer-item">
                            <text class="footer-icon">⏰</text>
                            <text class="footer-text">{{ item.petition_time }}</text>
                        </view>
                    </view>

                    <view class="reason-section" v-if="item.paper.whether_type != 0 || (item.paper.whether_type == 0 && item.acceptance_status == 0)">
                        <view class="reason-content">
                            <text class="reason-icon">{{ item.paper.whether_type != 0 ? '⚠️' : '🔍' }}</text>
                            <text class="reason-text">
                                {{ item.paper.whether_type != 0 ? item.paper.whether_reason : '正在核查' }}
                            </text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <view class="empty-state" v-if="info_list.length == 0">
                <view class="empty-icon">📭</view>
                <view class="empty-title">暂无举报记录</view>
                <view class="empty-desc">您还没有任何举报投诉记录</view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            info_list: [],
            current: 'tab1',
            page: 1,
            sc_msg: false,
            id: '',
            sc_text: ''
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        //this.get_user_report();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_user_report();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },

    methods: {
        handleChange(detail) {
            this.current = detail.currentTarget.dataset.key;
            this.get_user_report();
        },



        // 获取帖子状态图标
        getStatusIcon(item) {
            if (item.paper.whether_type != 0) {
                return '❌';
            } else if (item.paper.study_status == 2) {
                return '↩️';
            } else {
                return '⏳';
            }
        },

        // 获取帖子状态文本
        getStatusText(item) {
            if (item.paper.whether_type != 0) {
                return '已处理';
            } else if (item.paper.study_status == 2) {
                return '被打回';
            } else {
                return '处理中';
            }
        },



        // 获取回复状态图标
        getReplyStatusIcon(item) {
            if (item.paper.whether_type != 0) {
                return '❌';
            } else if (item.acceptance_status == 0) {
                return '🔍';
            } else {
                return '⏳';
            }
        },

        // 获取回复状态文本
        getReplyStatusText(item) {
            if (item.paper.whether_type != 0) {
                return '已处理';
            } else if (item.acceptance_status == 0) {
                return '核查中';
            } else {
                return '处理中';
            }
        },

        /**
         * 获取删帖
         */
        get_user_report() {
            var b = app.globalData.api_root + 'User/get_user_report';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.is_type = this.current;
            http.POST(b, {
                params: params,
                success: (res)=> {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.info_list = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },

        /**
         * 申诉
         */
        user_mutter(e) {
            console.log(e);
            this.sc_msg = true;
            this.id = e.currentTarget.dataset.key;
        },

        /**
         *
         */
        is_sc_text(e) {
            this.sc_text = e.detail.value;
        },

        /**
         * 隐藏窗口
         */
        hideModal() {
            this.sc_msg = false;
        },

        /**
         * 申诉
         */
        do_user_mutter() {
            var b = app.globalData.api_root + 'User/do_paper_mutter';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.info_list[this.id]['id'];
            params.tory_id = this.info_list[this.id]['tory_id'];
            params.is_reply = this.info_list[this.id]['is_reply'];
            params.tale_content = this.sc_text;
            http.POST(b, {
                params: params,
                success: (res)=> {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.sc_msg = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        //that.get_user_banned();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
page {
    background-color: #f8f9fa;
}

.report-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 头部标题样式 */
.header-title {
    color: #2c2b2b;
    font-weight: 600;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 标签页容器 */
.tab-container {
    position: relative;
    background: #ffffff;
    padding: 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    border-bottom: 1rpx solid #f1f3f4;
}

.tab-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    position: relative;
}

.tab-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 60rpx;
    flex: 1;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab-item.active {
    background: rgba(0, 129, 255, 0.05);
}

.tab-item.active .tab-text {
    color: #0081ff;
    font-weight: 700;
}

.tab-item.active .tab-icon {
    transform: scale(1.1);
}

.tab-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
    transition: all 0.3s ease;
}

.tab-text {
    font-size: 30rpx;
    font-weight: 600;
    color: #666666;
    transition: all 0.3s ease;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #0081ff 0%, #1cbbb4 100%);
    border-radius: 2rpx 2rpx 0 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 120rpx;
}

.indicator-tab1 {
    left: calc(25% - 60rpx);
}

.indicator-tab2 {
    left: calc(75% - 60rpx);
}

/* 内容容器 */
.content-container {
    padding: 30rpx 20rpx;
}

/* 举报卡片样式 */
.report-card {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.report-card:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx 20rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1rpx solid #f1f3f4;
}

.type-badge {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
}

.post-badge {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: #ffffff;
}

.reply-badge {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: #ffffff;
}

.badge-icon {
    margin-right: 8rpx;
    font-size: 28rpx;
}

.badge-text {
    font-weight: 600;
}

.status-badge {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 500;
}

.status-processing {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #d68910;
}

.status-checking {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #1b4f72;
}

.status-rejected {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #c0392b;
}

.status-returned {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    color: #8e44ad;
}

.status-icon {
    margin-right: 6rpx;
    font-size: 24rpx;
}

/* 卡片内容 */
.card-content {
    padding: 25rpx 30rpx;
}

.content-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.5;
    margin-bottom: 20rpx;
    word-break: break-all;
}

.content-description {
    display: flex;
    align-items: flex-start;
    background: #f8f9fa;
    padding: 20rpx;
    border-radius: 12rpx;
    border-left: 4rpx solid #667eea;
}

.desc-icon {
    margin-right: 12rpx;
    font-size: 28rpx;
    margin-top: 2rpx;
}

.desc-text {
    flex: 1;
    font-size: 28rpx;
    color: #5a6c7d;
    line-height: 1.6;
    word-break: break-all;
}

/* 卡片底部 */
.card-footer {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background: #f8f9fa;
    border-top: 1rpx solid #e9ecef;
}

.footer-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #6c757d;
}

.footer-icon {
    margin-right: 8rpx;
    font-size: 26rpx;
}

.footer-text {
    font-weight: 500;
}

/* 原因区域 */
.reason-section {
    padding: 20rpx 30rpx 25rpx;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-top: 1rpx solid #feb2b2;
}

.reason-content {
    display: flex;
    align-items: flex-start;
}

.reason-icon {
    margin-right: 12rpx;
    font-size: 28rpx;
    margin-top: 2rpx;
}

.reason-text {
    flex: 1;
    font-size: 26rpx;
    color: #c53030;
    font-weight: 500;
    line-height: 1.5;
    word-break: break-all;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
    text-align: center;
}

.empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
}

.empty-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 15rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #a0aec0;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
    .card-footer {
        flex-direction: column;
        gap: 15rpx;
    }

    .tab-item {
        padding: 25rpx 40rpx;
    }

    .tab-icon {
        font-size: 28rpx;
        margin-right: 10rpx;
    }

    .tab-text {
        font-size: 28rpx;
    }

    .content-container {
        padding: 20rpx 15rpx;
    }
}
</style>
