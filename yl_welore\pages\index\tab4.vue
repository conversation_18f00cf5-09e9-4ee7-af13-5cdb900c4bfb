<template>
    <view>
        <swiper
            v-if="sw_info.length > 0"
            previous-margin="5rpx"
            next-margin="5rpx"
            class="card-swiper square-dot"
            :indicator-dots="true"
            :circular="true"
            :autoplay="true"
            interval="5000"
            duration="500"
            @change="cardSwiper"
            indicator-color="#8799a3"
            indicator-active-color="#0081ff"
        >
            <swiper-item :class="cardCur == index ? 'cur' : ''" v-for="(item, index) in sw_info" :key="index">
                <view
                    class="swiper-item"
                    @tap.stop.prevent="open_navigator"
                    :data-src="item.playbill_url"
                    :data-path="item.wx_app_url"
                    :data-type="item.practice_type"
                    :data-url="item.url"
                >
                    <image mode="aspectFill" :src="item.playbill_url" />
                </view>
            </swiper-item>
        </swiper>
        <Home @get_nearby_realms="get_nearby_realms" @set_one="set_one" @top_home_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url" :data="parentData"></Home>
        <view style="width: 100%; height: 1px; background-color: #f9f9f9; margin: 5px 0px 20px 0px"></view>
        <view class="grid margin-bottom text-center">
            <view style="margin-left: 30px">
                <view @tap="handleChange" data-key="tab1" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab1' ? '_this' : ''" :style="current == 'tab1' ? 'font-size:20px;' : 'font-size:16px;'">首页</text>
                    <view v-if="current == 'tab1'" style="width: 6px; height: 6px; background-color: #3399ff; border-radius: 50%; margin: 0 auto"></view>
                </view>
            </view>
            <view style="margin: 0px 30px">
                <view @tap="handleChange" data-key="tab3" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab3' ? '_this' : ''" :style="current == 'tab3' ? 'font-size:20px;' : 'font-size:16px;'">推荐</text>
                    <view v-if="current == 'tab3'" style="width: 6px; height: 6px; background-color: #3399ff; border-radius: 50%; margin: 0 auto"></view>
                </view>
            </view>
            <view class="">
                <view @tap="handleChange" data-key="tab2" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab2' ? '_this' : ''" :style="';' + (current == 'tab2' ? 'font-size:20px;' : 'font-size:16px;')">关注</text>
                    <view v-if="current == 'tab2'" style="width: 6px; height: 6px; background-color: #3399ff; border-radius: 50%; margin: 0 auto"></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import Home from './home.vue';
export default {
    components: {
        Home 
    },
    props: ['data', 'compName'],
    computed: {
        parentData() {
            return this.$parent.$data;
        },
        sw_info() {
            return this.$parent.$data.sw_info;
        },
        cardCur() {
            return this.$parent.$data.cardCur;
        },
        current() {
            return this.$parent.$data.current;
        },
        home_list() {
            return this.$parent.$data.home_list;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        open_navigator(e) {
            this.$emit('open_navigator', e);
        },
        handleChange(e) {
            this.$emit('handleChange', e);
        },
        gambit_list(e) {
            this.$emit('gambit_list', e);
        },
        cardSwiper(e) {
            this.$emit('cardSwiper', e);
        },
        set_one(e) {
            this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        },
        get_nearby_realms(e){
            this.$emit('get_nearby_realms', e);
        }
    }
};
</script>
<style></style>
